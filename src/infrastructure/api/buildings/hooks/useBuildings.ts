/**
 * Building React Query Hooks
 * 
 * Custom hooks for building-related API operations using React Query
 */

import { useQuery, useMutation, useQueryClient, type UseQueryOptions, type UseMutationOptions } from '@tanstack/react-query';
import { buildingService } from '../building.service';
import type {
    GetBuildingsApiResponse,
    GetBuildingApiResponse,
    CreateBuildingApiResponse,
    GetBuildingsQueryParams,
    CreateBuildingRequest,
    UpdateBuildingRequest,
    ApiBuilding,
} from '../building.types';

// Query Keys
export const buildingQueryKeys = {
    all: ['buildings'] as const,
    lists: () => [...buildingQueryKeys.all, 'list'] as const,
    list: (params: GetBuildingsQueryParams) => [...buildingQueryKeys.lists(), params] as const,
    details: () => [...buildingQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...buildingQueryKeys.details(), id] as const,
    active: () => [...buildingQueryKeys.all, 'active'] as const,
    search: (searchTerm: string) => [...buildingQueryKeys.all, 'search', searchTerm] as const,
};

// Hook Options Types
type UseBuildingsOptions = Omit<UseQueryOptions<GetBuildingsApiResponse>, 'queryKey' | 'queryFn'>;
type UseBuildingOptions = Omit<UseQueryOptions<GetBuildingApiResponse>, 'queryKey' | 'queryFn'>;
type UseCreateBuildingOptions = UseMutationOptions<CreateBuildingApiResponse, Error, CreateBuildingRequest>;
type UseUpdateBuildingOptions = UseMutationOptions<CreateBuildingApiResponse, Error, { id: number; data: UpdateBuildingRequest }>;
type UseDeleteBuildingOptions = UseMutationOptions<void, Error, number>;

/**
 * Hook to fetch all buildings with optional filtering and pagination
 */
export const useBuildings = (
    params: GetBuildingsQueryParams = {},
    options?: UseBuildingsOptions,
) => {
    return useQuery({
        queryKey: buildingQueryKeys.list(params),
        queryFn: () => buildingService.getBuildings(params),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        ...options,
    });
};

/**
 * Hook to fetch a single building by ID
 */
export const useBuilding = (
    id: number,
    options?: UseBuildingOptions,
) => {
    return useQuery({
        queryKey: buildingQueryKeys.detail(id),
        queryFn: () => buildingService.getBuildingById(id),
        enabled: !!id,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        ...options,
    });
};

/**
 * Hook to fetch active buildings only
 */
export const useActiveBuildings = (
    params: Omit<GetBuildingsQueryParams, 'is_active'> = {},
    options?: UseBuildingsOptions,
) => {
    return useQuery({
        queryKey: buildingQueryKeys.list({ ...params, is_active: true }),
        queryFn: () => buildingService.getActiveBuildings(params),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        ...options,
    });
};

/**
 * Hook to search buildings
 */
export const useSearchBuildings = (
    searchTerm: string,
    params: Omit<GetBuildingsQueryParams, 'search'> = {},
    options?: UseBuildingsOptions,
) => {
    return useQuery({
        queryKey: buildingQueryKeys.search(searchTerm),
        queryFn: () => buildingService.searchBuildings(searchTerm, params),
        enabled: !!searchTerm && searchTerm.length > 0,
        staleTime: 2 * 60 * 1000, // 2 minutes for search results
        gcTime: 5 * 60 * 1000, // 5 minutes
        ...options,
    });
};

/**
 * Hook to create a new building
 */
export const useCreateBuilding = (options?: UseCreateBuildingOptions) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (buildingData: CreateBuildingRequest) => buildingService.createBuilding(buildingData),
        onSuccess: (data) => {
            // Invalidate and refetch buildings list
            queryClient.invalidateQueries({ queryKey: buildingQueryKeys.lists() });
            
            // Add the new building to the cache
            queryClient.setQueryData(
                buildingQueryKeys.detail(data.data.id),
                {
                    response_code: data.response_code,
                    response_message: data.response_message,
                    response_message_ar: data.response_message_ar,
                    data: data.data,
                } as GetBuildingApiResponse
            );
        },
        ...options,
    });
};

/**
 * Hook to update an existing building
 */
export const useUpdateBuilding = (options?: UseUpdateBuildingOptions) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: number; data: UpdateBuildingRequest }) => 
            buildingService.updateBuilding(id, data),
        onSuccess: (data, variables) => {
            // Invalidate and refetch buildings list
            queryClient.invalidateQueries({ queryKey: buildingQueryKeys.lists() });
            
            // Update the specific building in cache
            queryClient.setQueryData(
                buildingQueryKeys.detail(variables.id),
                {
                    response_code: data.response_code,
                    response_message: data.response_message,
                    response_message_ar: data.response_message_ar,
                    data: data.data,
                } as GetBuildingApiResponse
            );
        },
        ...options,
    });
};

/**
 * Hook to delete a building
 */
export const useDeleteBuilding = (options?: UseDeleteBuildingOptions) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: number) => buildingService.deleteBuilding(id),
        onSuccess: (_, deletedId) => {
            // Invalidate and refetch buildings list
            queryClient.invalidateQueries({ queryKey: buildingQueryKeys.lists() });
            
            // Remove the deleted building from cache
            queryClient.removeQueries({ queryKey: buildingQueryKeys.detail(deletedId) });
        },
        ...options,
    });
};

/**
 * Hook to prefetch buildings
 */
export const usePrefetchBuildings = () => {
    const queryClient = useQueryClient();

    return (params: GetBuildingsQueryParams = {}) => {
        queryClient.prefetchQuery({
            queryKey: buildingQueryKeys.list(params),
            queryFn: () => buildingService.getBuildings(params),
            staleTime: 5 * 60 * 1000, // 5 minutes
        });
    };
};

/**
 * Hook to get buildings data from cache without triggering a request
 */
export const useBuildingsCache = (params: GetBuildingsQueryParams = {}) => {
    const queryClient = useQueryClient();
    return queryClient.getQueryData<GetBuildingsApiResponse>(buildingQueryKeys.list(params));
};

/**
 * Hook to get building data from cache without triggering a request
 */
export const useBuildingCache = (id: number) => {
    const queryClient = useQueryClient();
    return queryClient.getQueryData<GetBuildingApiResponse>(buildingQueryKeys.detail(id));
};
