/**
 * Simple Building Hooks
 * بس للget buildings
 */

import { useQuery, type UseQueryOptions } from '@tanstack/react-query';
import { buildingService } from '../building.service';
import type { GetBuildingsApiResponse } from '../building.types';

// Query Keys - بسيط
export const buildingQueryKeys = {
    all: ['buildings'] as const,
    list: () => [...buildingQueryKeys.all, 'list'] as const,
};

// Hook Options Types - بسيط
type UseBuildingsOptions = Omit<UseQueryOptions<GetBuildingsApiResponse>, 'queryKey' | 'queryFn'>;

/**
 * Hook to get buildings - بسيط جداً
 */
export const useBuildings = (options?: UseBuildingsOptions) => {
    return useQuery({
        queryKey: buildingQueryKeys.list(),
        queryFn: () => buildingService.getBuildings(),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        ...options,
    });
};

// خلاص كدا - بس useBuildings
