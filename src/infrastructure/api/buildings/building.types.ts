/**
 * Building API Types
 * TypeScript interfaces for the buildings API endpoints
 */

// Base API Response Structure
export interface BaseApiResponse {
    response_code: string;
    response_message: string;
    response_message_ar: string;
}

// Building Entity from API
export interface ApiBuilding {
    id: number;
    name: string;
    code: string;
    description: string;
    address: string;
    is_active: boolean;
    zone_count: number;
    floor_count: number;
    room_count: number;
    door_count: number;
    device_count: number;
    event_count: number;
    has_alerts: boolean;
    create_date: string; // ISO-8601 format
    write_date: string; // ISO-8601 format
    zones_url: string;
    floors_url: string;
    devices_url: string;
}

// Pagination Information
export interface PaginationInfo {
    limit: number;
    offset: number;
    has_more: boolean;
}

// Buildings API Response Data
export interface GetBuildingsResponseData {
    buildings: ApiBuilding[];
    total_count: number;
    returned_count: number;
    pagination: PaginationInfo;
}

// Complete Buildings API Response
export interface GetBuildingsApiResponse extends BaseApiResponse {
    data: GetBuildingsResponseData;
}

// Query Parameters for Buildings API
export interface GetBuildingsQueryParams {
    limit?: number;
    offset?: number;
    is_active?: boolean;
    search?: string;
    code?: string;
}

// Single Building API Response
export interface GetBuildingApiResponse extends BaseApiResponse {
    data: ApiBuilding;
}

// Create Building Request
export interface CreateBuildingRequest {
    name: string;
    code: string;
    description?: string;
    address?: string;
    is_active?: boolean;
}

// Update Building Request
// export interface UpdateBuildingRequest extends Partial<CreateBuildingRequest> {}

// Create/Update Building Response
export interface CreateBuildingApiResponse extends BaseApiResponse {
    data: ApiBuilding;
}
