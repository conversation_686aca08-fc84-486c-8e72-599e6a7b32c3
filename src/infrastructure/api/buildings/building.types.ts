// types.ts
export interface Building {
  id: number;
  name: string;
  code: string;
  description: string;
  address: string;
  is_active: boolean;
  zone_count: number;
  floor_count: number;
  room_count: number;
  door_count: number;
  device_count: number;
  event_count: number;
  has_alerts: boolean;
  create_date: string;
  write_date: string;
  zones_url: string;
  floors_url: string;
  devices_url: string;
}

export interface GetBuildingsApiResponse {
  response_code: string;
  response_message: string;
  response_message_ar: string;
  data: {
    buildings: Building[];
    total_count: number;
    returned_count: number;
    pagination: {
      limit: number;
      offset: number;
      has_more: boolean;
    };
  };
}
