# Building Service Testing Guide

دليل شامل لتيست building service

## 🚀 طرق التيست

### 1. تيست في المتصفح (الأسهل)

```bash
# شغل المشروع
npm run dev

# روح على الصفحة
http://localhost:3000/test-buildings
```

### 2. تيست في Console

افتح Developer Console (F12) وجرب:

```javascript
// تيست سريع
await quickTestBuildings()

// تيست شامل
await testBuildingService()
```

### 3. تيست في Component

```typescript
import { useBuildings } from '@/infrastructure/api/buildings';

function TestComponent() {
    const { data, isLoading, error } = useBuildings();
    
    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;
    
    return <div>Buildings: {data?.data.buildings.length}</div>;
}
```

## 🔧 إعدادات التيست

### Mock Data vs Real API

في `src/shared/config/app.config.ts`:

```typescript
features: {
    enableDebugTools: true  // يستخدم mock data
    enableDebugTools: false // يستخدم real API
}
```

### Environment Variables

```bash
# في .env.local
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
NEXT_PUBLIC_ENABLE_DEBUG_TOOLS=true
```

## 📋 Test Cases

### ✅ Basic Tests

1. **Get Buildings** - جلب المباني
   ```typescript
   const response = await buildingService.getBuildings();
   expect(response.response_code).toBe('000');
   expect(response.data.buildings).toBeDefined();
   ```

2. **Get Active Buildings** - جلب المباني النشطة
   ```typescript
   const response = await buildingService.getBuildings({ is_active: true });
   expect(response.data.buildings.every(b => b.is_active)).toBe(true);
   ```

3. **Pagination** - التصفح
   ```typescript
   const response = await buildingService.getBuildings({ limit: 5, offset: 0 });
   expect(response.data.returned_count).toBeLessThanOrEqual(5);
   ```

### ✅ React Query Tests

1. **useBuildings Hook**
   ```typescript
   const { data, isLoading, error } = useBuildings();
   // تأكد إن البيانات بتيجي صح
   ```

2. **Cache Behavior**
   ```typescript
   // التأكد إن البيانات بتتحفظ في cache
   const cachedData = useBuildingsCache();
   ```

### ✅ Error Handling Tests

1. **Network Error**
   ```typescript
   // قطع النت وشوف لو الerror handling شغال
   ```

2. **Invalid Response**
   ```typescript
   // جرب response غلط وشوف لو بيتعامل معاه
   ```

## 🐛 Common Issues & Solutions

### Issue 1: "Cannot find module"
```bash
# تأكد إن كل الimports صحيحة
npm run type-check
```

### Issue 2: "Hook can only be called inside component"
```typescript
// استخدم الhooks جوا components بس
// لو عايز تستخدم الservice خارج component، استخدم buildingService مباشرة
```

### Issue 3: "Network Error"
```typescript
// تأكد إن الAPI شغال أو enableDebugTools = true
```

## 📊 Test Results

### Expected Mock Data Response

```json
{
  "response_code": "000",
  "response_message": "Success",
  "response_message_ar": "نجح",
  "data": {
    "buildings": [
      {
        "id": 1,
        "name": "Building A",
        "code": "A",
        "is_active": true,
        // ... more fields
      }
    ],
    "total_count": 2,
    "returned_count": 2,
    "pagination": {
      "limit": 100,
      "offset": 0,
      "has_more": false
    }
  }
}
```

## 🔍 Debug Tips

1. **Check Console** - شوف console للlogs
2. **Check Network Tab** - شوف network requests
3. **Check React Query DevTools** - لو مفعل
4. **Check App Config** - تأكد من الإعدادات

## 📝 Manual Testing Checklist

- [ ] الصفحة بتحمل بدون errors
- [ ] البيانات بتظهر صح
- [ ] Loading state بيشتغل
- [ ] Error handling بيشتغل
- [ ] Pagination بيشتغل (لو موجود)
- [ ] Search بيشتغل (لو موجود)
- [ ] Mock data vs Real API بيتبدل صح

## 🚨 Red Flags

- Console errors
- Infinite loading
- Network errors مع mock data
- TypeScript errors
- Missing data fields

## 📞 Need Help?

1. Check console for errors
2. Check network tab
3. Verify app configuration
4. Check if API is running (for real API tests)
