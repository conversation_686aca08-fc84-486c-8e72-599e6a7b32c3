/**
 * Buildings API Configuration
 *
 * Centralized API configuration for the Buildings API
 * using the Fetchy library with proper configuration.
 */

import { Fetchy } from '@/shared/lib/Fetchy';
import { appConfig } from '@/shared/config/app.config';
import { logger } from '@/infrastructure/logging';

const config = appConfig.getConfig();

// Create Buildings API instance with configuration
export const buildingsApi = Fetchy.getInstance({
    baseURL: config.api.baseUrl,
    timeout: config.api.timeout,
    retries: config.api.retryAttempts,
    retryDelay: config.api.retryDelay,
    headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'Accept-Language': 'ar',
    },
    Logger: logger,
});

// Add request interceptor for authentication if needed
buildingsApi.addRequestInterceptor((config) => {
    // Add authentication token if available
    const authToken = localStorage.getItem('auth_token');
    if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
    }

    return config;
});

// Add response interceptor for logging and error handling
buildingsApi.addResponseInterceptor(
    // Success handler
    (response) => {
        logger.info('Buildings API Response:', {
            status: response.status,
            url: response.config?.url,
            method: response.config?.method?.toUpperCase(),
        });
        return response;
    },
    // Error handler
    (error) => {
        logger.error('Buildings API Error:', {
            status: error.response?.status,
            message: error.message,
            url: error.config?.url,
            method: error.config?.method?.toUpperCase(),
        });

        // Handle specific error cases
        if (error.response?.status === 401) {
            // Handle unauthorized access
            logger.warn('Unauthorized access to Buildings API');
            // Could redirect to login or refresh token here
        }

        return Promise.reject(error);
    },
);

export default buildingsApi;
