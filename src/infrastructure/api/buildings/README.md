# Buildings API Service

This module provides a complete service layer for managing buildings through the `/api/v1/buildings` endpoint, following the Fetchy library patterns and best practices.

## Features

- ✅ **Type-safe API calls** with comprehensive TypeScript interfaces
- ✅ **React Query integration** with custom hooks for caching and state management
- ✅ **Error handling** with proper logging and interceptors
- ✅ **Mock data support** for development and testing
- ✅ **Singleton service pattern** for consistent API access
- ✅ **Automatic cache invalidation** on mutations
- ✅ **Search and filtering** capabilities
- ✅ **Pagination support**

## Quick Start

### Basic Usage

```typescript
import { useBuildings, buildingService } from '@/infrastructure/api/buildings';

// Using React Query hook (recommended)
function BuildingsComponent() {
    const { data, isLoading, error } = useBuildings();
    
    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;
    
    return (
        <div>
            {data?.data.buildings.map(building => (
                <div key={building.id}>{building.name}</div>
            ))}
        </div>
    );
}

// Using service directly
async function fetchBuildings() {
    try {
        const response = await buildingService.getBuildings();
        console.log(response.data.buildings);
    } catch (error) {
        console.error('Failed to fetch buildings:', error);
    }
}
```

## API Reference

### Service Methods

#### `getBuildings(params?: GetBuildingsQueryParams)`
Fetch all buildings with optional filtering and pagination.

```typescript
const response = await buildingService.getBuildings({
    limit: 10,
    offset: 0,
    is_active: true,
    search: 'Building A'
});
```

#### `getBuildingById(id: number)`
Fetch a single building by ID.

```typescript
const response = await buildingService.getBuildingById(1);
```

#### `createBuilding(data: CreateBuildingRequest)`
Create a new building.

```typescript
const response = await buildingService.createBuilding({
    name: 'New Building',
    code: 'NEW',
    description: 'A new building',
    address: '123 Main St',
    is_active: true
});
```

#### `updateBuilding(id: number, data: UpdateBuildingRequest)`
Update an existing building.

```typescript
const response = await buildingService.updateBuilding(1, {
    name: 'Updated Building Name'
});
```

#### `deleteBuilding(id: number)`
Delete a building.

```typescript
await buildingService.deleteBuilding(1);
```

### React Query Hooks

#### `useBuildings(params?, options?)`
Hook to fetch buildings with optional parameters.

```typescript
const { data, isLoading, error, refetch } = useBuildings({
    limit: 20,
    is_active: true
}, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3
});
```

#### `useBuilding(id, options?)`
Hook to fetch a single building.

```typescript
const { data, isLoading, error } = useBuilding(buildingId, {
    enabled: !!buildingId
});
```

#### `useActiveBuildings(params?, options?)`
Hook to fetch only active buildings.

```typescript
const { data, isLoading, error } = useActiveBuildings({
    limit: 10
});
```

#### `useSearchBuildings(searchTerm, params?, options?)`
Hook to search buildings by name, code, or description.

```typescript
const { data, isLoading, error } = useSearchBuildings('Building A', {}, {
    enabled: searchTerm.length > 2
});
```

#### Mutation Hooks

```typescript
// Create building
const createMutation = useCreateBuilding({
    onSuccess: (data) => console.log('Created:', data.data),
    onError: (error) => console.error('Error:', error)
});

// Update building
const updateMutation = useUpdateBuilding({
    onSuccess: (data) => console.log('Updated:', data.data)
});

// Delete building
const deleteMutation = useDeleteBuilding({
    onSuccess: () => console.log('Deleted successfully')
});

// Usage
createMutation.mutate(newBuildingData);
updateMutation.mutate({ id: 1, data: updateData });
deleteMutation.mutate(buildingId);
```

## Types

### API Response Types

```typescript
interface GetBuildingsApiResponse {
    response_code: string;
    response_message: string;
    response_message_ar: string;
    data: {
        buildings: ApiBuilding[];
        total_count: number;
        returned_count: number;
        pagination: {
            limit: number;
            offset: number;
            has_more: boolean;
        };
    };
}

interface ApiBuilding {
    id: number;
    name: string;
    code: string;
    description: string;
    address: string;
    is_active: boolean;
    zone_count: number;
    floor_count: number;
    room_count: number;
    door_count: number;
    device_count: number;
    event_count: number;
    has_alerts: boolean;
    create_date: string;
    write_date: string;
    zones_url: string;
    floors_url: string;
    devices_url: string;
}
```

### Request Types

```typescript
interface GetBuildingsQueryParams {
    limit?: number;
    offset?: number;
    is_active?: boolean;
    search?: string;
    code?: string;
}

interface CreateBuildingRequest {
    name: string;
    code: string;
    description?: string;
    address?: string;
    is_active?: boolean;
}

interface UpdateBuildingRequest extends Partial<CreateBuildingRequest> {}
```

## Configuration

### Mock Data vs Real API

The service automatically switches between mock data and real API calls based on the `enableDebugTools` configuration:

```typescript
// In app.config.ts
features: {
    enableDebugTools: true // Uses mock data
    enableDebugTools: false // Uses real API
}
```

### Environment Variables

```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
NEXT_PUBLIC_API_TIMEOUT=30000
NEXT_PUBLIC_API_RETRY_ATTEMPTS=3
NEXT_PUBLIC_API_RETRY_DELAY=1000
```

## Error Handling

The service includes comprehensive error handling:

- **Network errors**: Automatic retries with exponential backoff
- **Authentication errors**: Automatic token refresh or redirect to login
- **Validation errors**: Detailed error messages from the API
- **Logging**: All requests and responses are logged for debugging

## Testing

### Unit Testing

```typescript
import { buildingService } from '@/infrastructure/api/buildings';

// Mock the API
jest.mock('@/infrastructure/api/buildings/buildings-api.config');

describe('BuildingService', () => {
    it('should fetch buildings successfully', async () => {
        const mockResponse = { /* mock data */ };
        buildingsApi.get.mockResolvedValue({ data: mockResponse });

        const result = await buildingService.getBuildings();
        
        expect(result).toEqual(mockResponse);
        expect(buildingsApi.get).toHaveBeenCalledWith('/api/v1/buildings', {
            params: {}
        });
    });
});
```

### Integration Testing

```typescript
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BuildingsList } from './BuildingsList';

const createTestQueryClient = () => new QueryClient({
    defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
    }
});

describe('BuildingsList', () => {
    it('should display buildings when data loads successfully', async () => {
        const queryClient = createTestQueryClient();
        
        render(
            <QueryClientProvider client={queryClient}>
                <BuildingsList />
            </QueryClientProvider>
        );

        await waitFor(() => {
            expect(screen.getByText('Building A')).toBeInTheDocument();
        });
    });
});
```

## Best Practices

1. **Always use React Query hooks** in components for automatic caching and state management
2. **Handle loading and error states** properly in your UI
3. **Use TypeScript interfaces** for type safety
4. **Implement proper error boundaries** for graceful error handling
5. **Use the service directly** only in non-React contexts (e.g., utility functions)
6. **Leverage query invalidation** for real-time updates after mutations
7. **Use search and filtering** to reduce data transfer and improve performance

## Example Component

See `src/components/examples/BuildingsList.example.tsx` for a complete example of how to use the building service in a React component with full CRUD operations.
