/**
 * Building Service - Simple Version
 * بس للget buildings
 */

import buildingsApi from './buildings-api.config';
import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app.config';
import type { GetBuildingsApiResponse } from './building.types';

// Mock data بسيط
const mockBuildingsResponse: GetBuildingsApiResponse = {
    response_code: '000',
    response_message: 'Success',
    response_message_ar: 'نجح',
    data: {
        buildings: [
            {
                id: 1,
                name: 'Building A',
                code: 'A',
                description: 'Main headquarters building with offices, meeting rooms, and data center',
                address: '123 Technology Drive, Innovation City, IC 12345',
                is_active: true,
                zone_count: 2,
                floor_count: 3,
                room_count: 4,
                door_count: 2,
                device_count: 10,
                event_count: 22,
                has_alerts: false,
                create_date: '2025-09-23T10:47:38.352278',
                write_date: '2025-09-23T10:47:38.352278',
                zones_url: '/api/v1/buildings/1/zones',
                floors_url: '/api/v1/buildings/1/floors',
                devices_url: '/api/v1/buildings/1/devices',
            },
            {
                id: 2,
                name: 'Building B',
                code: 'B',
                description: 'Primary distribution and logistics center',
                address: '456 Logistics Boulevard, Industrial Park, IP 67890',
                is_active: true,
                zone_count: 1,
                floor_count: 1,
                room_count: 1,
                door_count: 1,
                device_count: 2,
                event_count: 5,
                has_alerts: false,
                create_date: '2025-09-23T10:47:38.352278',
                write_date: '2025-09-23T10:47:38.352278',
                zones_url: '/api/v1/buildings/2/zones',
                floors_url: '/api/v1/buildings/2/floors',
                devices_url: '/api/v1/buildings/2/devices',
            },
        ],
        total_count: 2,
        returned_count: 2,
        pagination: {
            limit: 100,
            offset: 0,
            has_more: false,
        },
    },
};

export class BuildingService {
    private static instance: BuildingService;

    public static getInstance(): BuildingService {
        if (!BuildingService.instance) {
            BuildingService.instance = new BuildingService();
        }
        return BuildingService.instance;
    }

    /**
     * Get buildings - بسيط جداً
     */
    public async getBuildings(): Promise<GetBuildingsApiResponse> {
        logger.info('[BuildingService] fetching buildings');

        try {
            const config = appConfig.getConfig();

            // لو في debug mode استخدم mock data
            if (config.features.enableDebugTools) {
                logger.info('[BuildingService] Using mock data');
                return Promise.resolve(mockBuildingsResponse);
            }

            // لو مش في debug mode استخدم real API
            const response = await buildingsApi.get<GetBuildingsApiResponse>('/api/v1/buildings');
            return response.data;
        } catch (error: unknown) {
            logger.error('[BuildingService] Error fetching buildings:', error as Error);
            throw error;
        }
    }
}

export const buildingService = BuildingService.getInstance();
