// building.service.ts
import { fetchy } from '@/shared/lib/Fetchy';
import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { GetBuildingsApiResponse } from './building.types';

export class BuildingService {
  private static instance: BuildingService;

  public static getInstance(): BuildingService {
    if (!BuildingService.instance) {
      BuildingService.instance = new BuildingService();
    }
    return BuildingService.instance;
  }

  public async getBuildings(): Promise<GetBuildingsApiResponse> {
    logger.info('[BuildingService] fetching buildings');

    try {
      return this.realFetchBuildings();
    } catch (error: unknown) {
      logger.error('[BuildingService] Error fetching buildings:', error as Error);
      throw error;
    }
  }

  private async realFetchBuildings(): Promise<GetBuildingsApiResponse> {
    const response = await fetchy.get<GetBuildingsApiResponse>('buildings');

    // Validate response
    const validationResult = valy.validate(GetBuildingsApiResponse, response.data, 'buildings');
    if (!validationResult.success) {
      logger.error('[BuildingService] Invalid response from buildings API', validationResult.error);
      throw new Error('Invalid API response format');
    }

    return response.data;
  }
}

export const buildingService = BuildingService.getInstance();
