/**
 * Building Service
 * 
 * Service layer for handling all building-related API operations
 * following the Fetchy library patterns and best practices.
 */

import buildingsApi from './buildings-api.config';
import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app.config';
import type {
    ApiBuilding,
    GetBuildingsApiResponse,
    GetBuildingApiResponse,
    CreateBuildingApiResponse,
    GetBuildingsQueryParams,
    CreateBuildingRequest,
    UpdateBuildingRequest,
} from './building.types';

// Mock data for development
const mockBuildingsResponse: GetBuildingsApiResponse = {
    response_code: "000",
    response_message: "Success",
    response_message_ar: "نجح",
    data: {
        buildings: [
            {
                id: 1,
                name: "Building A",
                code: "A",
                description: "Main headquarters building with offices, meeting rooms, and data center",
                address: "123 Technology Drive, Innovation City, IC 12345",
                is_active: true,
                zone_count: 2,
                floor_count: 3,
                room_count: 4,
                door_count: 2,
                device_count: 10,
                event_count: 22,
                has_alerts: false,
                create_date: "2025-09-23T10:47:38.352278",
                write_date: "2025-09-23T10:47:38.352278",
                zones_url: "/api/v1/buildings/1/zones",
                floors_url: "/api/v1/buildings/1/floors",
                devices_url: "/api/v1/buildings/1/devices"
            },
            {
                id: 2,
                name: "Building B",
                code: "B",
                description: "Primary distribution and logistics center",
                address: "456 Logistics Boulevard, Industrial Park, IP 67890",
                is_active: true,
                zone_count: 1,
                floor_count: 1,
                room_count: 1,
                door_count: 1,
                device_count: 2,
                event_count: 5,
                has_alerts: false,
                create_date: "2025-09-23T10:47:38.352278",
                write_date: "2025-09-23T10:47:38.352278",
                zones_url: "/api/v1/buildings/2/zones",
                floors_url: "/api/v1/buildings/2/floors",
                devices_url: "/api/v1/buildings/2/devices"
            }
        ],
        total_count: 2,
        returned_count: 2,
        pagination: {
            limit: 100,
            offset: 0,
            has_more: false
        }
    }
};

/**
 * Building Service Class
 * Provides all CRUD operations for buildings
 */
export class BuildingService {
    private static instance: BuildingService;
    private readonly basePath = '/api/v1/buildings';

    private constructor() {}

    public static getInstance(): BuildingService {
        if (!BuildingService.instance) {
            BuildingService.instance = new BuildingService();
        }
        return BuildingService.instance;
    }

    /**
     * Get all buildings with optional filtering and pagination
     */
    public async getBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        logger.info('[BuildingService] fetching buildings with query params:', params);

        try {
            const config = appConfig.getConfig();
            if (config.features.enableDebugTools) {
                logger.info('[BuildingService] Using mock data for buildings');
                return this.fakeFetchBuildings(params);
            }
            return this.realFetchBuildings(params);
        } catch (error: unknown) {
            logger.error('[BuildingService] Error fetching buildings:', error as Error);
            throw error;
        }
    }

    /**
     * Get a single building by ID
     */
    public async getBuildingById(id: number): Promise<GetBuildingApiResponse> {
        logger.info(`[BuildingService] fetching building with ID: ${id}`);

        try {
            const config = appConfig.getConfig();
            if (config.features.enableDebugTools) {
                return this.fakeFetchBuildingById(id);
            }
            return this.realFetchBuildingById(id);
        } catch (error: unknown) {
            logger.error(`[BuildingService] Error fetching building ${id}:`, error as Error);
            throw error;
        }
    }

    /**
     * Create a new building
     */
    public async createBuilding(buildingData: CreateBuildingRequest): Promise<CreateBuildingApiResponse> {
        logger.info('[BuildingService] creating building:', buildingData);

        try {
            const response = await buildingsApi.post<CreateBuildingApiResponse>(this.basePath, {
                body: buildingData,
            });
            return response.data;
        } catch (error: unknown) {
            logger.error('[BuildingService] Error creating building:', error as Error);
            throw error;
        }
    }

    /**
     * Update an existing building
     */
    public async updateBuilding(id: number, buildingData: UpdateBuildingRequest): Promise<CreateBuildingApiResponse> {
        logger.info(`[BuildingService] updating building ${id}:`, buildingData);

        try {
            const response = await buildingsApi.patch<CreateBuildingApiResponse>(`${this.basePath}/${id}`, {
                body: buildingData,
            });
            return response.data;
        } catch (error: unknown) {
            logger.error(`[BuildingService] Error updating building ${id}:`, error as Error);
            throw error;
        }
    }

    /**
     * Delete a building
     */
    public async deleteBuilding(id: number): Promise<void> {
        logger.info(`[BuildingService] deleting building ${id}`);

        try {
            await buildingsApi.delete(`${this.basePath}/${id}`);
        } catch (error: unknown) {
            logger.error(`[BuildingService] Error deleting building ${id}:`, error as Error);
            throw error;
        }
    }

    /**
     * Get active buildings only
     */
    public async getActiveBuildings(params: Omit<GetBuildingsQueryParams, 'is_active'> = {}): Promise<GetBuildingsApiResponse> {
        return this.getBuildings({ ...params, is_active: true });
    }

    /**
     * Search buildings by name or code
     */
    public async searchBuildings(searchTerm: string, params: Omit<GetBuildingsQueryParams, 'search'> = {}): Promise<GetBuildingsApiResponse> {
        return this.getBuildings({ ...params, search: searchTerm });
    }

    // Private methods for real API calls
    private async realFetchBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        const response = await buildingsApi.get<GetBuildingsApiResponse>(this.basePath, {
            params: {
                ...params,
                ...(params.limit && { limit: params.limit }),
                ...(params.offset && { offset: params.offset }),
                ...(params.is_active !== undefined && { is_active: params.is_active }),
                ...(params.search && { search: params.search }),
                ...(params.code && { code: params.code }),
            },
        });
        return response.data;
    }

    private async realFetchBuildingById(id: number): Promise<GetBuildingApiResponse> {
        const response = await buildingsApi.get<GetBuildingApiResponse>(`${this.basePath}/${id}`);
        return response.data;
    }

    // Private methods for mock data
    private fakeFetchBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        logger.info('[BuildingService] Using mock data for buildings');
        
        let filteredBuildings = [...mockBuildingsResponse.data.buildings];
        
        // Apply filters
        if (params.is_active !== undefined) {
            filteredBuildings = filteredBuildings.filter(b => b.is_active === params.is_active);
        }
        
        if (params.search) {
            const searchLower = params.search.toLowerCase();
            filteredBuildings = filteredBuildings.filter(b => 
                b.name.toLowerCase().includes(searchLower) || 
                b.code.toLowerCase().includes(searchLower) ||
                b.description.toLowerCase().includes(searchLower)
            );
        }
        
        if (params.code) {
            filteredBuildings = filteredBuildings.filter(b => b.code === params.code);
        }
        
        // Apply pagination
        const limit = params.limit || 100;
        const offset = params.offset || 0;
        const paginatedBuildings = filteredBuildings.slice(offset, offset + limit);
        
        const mockResponse: GetBuildingsApiResponse = {
            ...mockBuildingsResponse,
            data: {
                buildings: paginatedBuildings,
                total_count: filteredBuildings.length,
                returned_count: paginatedBuildings.length,
                pagination: {
                    limit,
                    offset,
                    has_more: offset + limit < filteredBuildings.length
                }
            }
        };
        
        return Promise.resolve(mockResponse);
    }

    private fakeFetchBuildingById(id: number): Promise<GetBuildingApiResponse> {
        const building = mockBuildingsResponse.data.buildings.find(b => b.id === id);
        
        if (!building) {
            return Promise.reject(new Error(`Building with ID ${id} not found`));
        }
        
        const mockResponse: GetBuildingApiResponse = {
            response_code: "000",
            response_message: "Success",
            response_message_ar: "نجح",
            data: building
        };
        
        return Promise.resolve(mockResponse);
    }
}

// Export singleton instance
export const buildingService = BuildingService.getInstance();
