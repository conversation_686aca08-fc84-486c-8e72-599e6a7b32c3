/**
 * Buildings API - Main Export
 * 
 * Centralized exports for all Buildings API services, hooks, and types
 */

// API Configuration
export { default as buildingsApi } from './buildings-api.config';

// Services
export { BuildingService, buildingService } from './building.service';

// React Query Hooks
export {
    // Query Hooks
    useBuildings,
    useBuilding,
    useActiveBuildings,
    useSearchBuildings,
    usePrefetchBuildings,
    useBuildingsCache,
    useBuildingCache,

    // Mutation Hooks
    useCreateBuilding,
    useUpdateBuilding,
    useDeleteBuilding,

    // Query Keys
    buildingQueryKeys,
} from './hooks/useBuildings';

// Types
export type {
    // API Response Types
    BaseApiResponse,
    GetBuildingsApiResponse,
    GetBuildingApiResponse,
    CreateBuildingApiResponse,
    GetBuildingsResponseData,
    PaginationInfo,

    // Entity Types
    ApiBuilding,

    // Request Types
    GetBuildingsQueryParams,
    CreateBuildingRequest,
    UpdateBuildingRequest,
} from './building.types';
