/**
 * Buildings List Example Component
 * 
 * Example component demonstrating how to use the building service
 * with React Query hooks following the Fetchy library patterns.
 */

import React, { useState } from 'react';
import { 
    useBuildings, 
    useActiveBuildings, 
    useSearchBuildings,
    useCreateBuilding,
    useUpdateBuilding,
    useDeleteBuilding,
    type GetBuildingsQueryParams,
    type CreateBuildingRequest,
    type ApiBuilding 
} from '@/infrastructure/api/buildings';

interface BuildingsListProps {
    showInactiveBuildings?: boolean;
}

export function BuildingsList({ showInactiveBuildings = false }: BuildingsListProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedBuilding, setSelectedBuilding] = useState<ApiBuilding | null>(null);
    const [isCreating, setIsCreating] = useState(false);

    // Query parameters
    const queryParams: GetBuildingsQueryParams = {
        limit: 10,
        offset: 0,
        ...(showInactiveBuildings ? {} : { is_active: true })
    };

    // Hooks for fetching data
    const {
        data: buildingsData,
        isLoading,
        error,
        refetch
    } = useBuildings(queryParams, {
        retry: 3,
        retryDelay: 1000,
    });

    // Search hook (only active when searchTerm is provided)
    const {
        data: searchResults,
        isLoading: isSearching
    } = useSearchBuildings(searchTerm, {}, {
        enabled: searchTerm.length > 2
    });

    // Mutation hooks
    const createBuildingMutation = useCreateBuilding({
        onSuccess: (data) => {
            console.log('Building created successfully:', data.data);
            setIsCreating(false);
            // The hook automatically invalidates and refetches the buildings list
        },
        onError: (error) => {
            console.error('Failed to create building:', error);
        }
    });

    const updateBuildingMutation = useUpdateBuilding({
        onSuccess: (data) => {
            console.log('Building updated successfully:', data.data);
            setSelectedBuilding(null);
        },
        onError: (error) => {
            console.error('Failed to update building:', error);
        }
    });

    const deleteBuildingMutation = useDeleteBuilding({
        onSuccess: () => {
            console.log('Building deleted successfully');
            setSelectedBuilding(null);
        },
        onError: (error) => {
            console.error('Failed to delete building:', error);
        }
    });

    // Event handlers
    const handleCreateBuilding = () => {
        const newBuilding: CreateBuildingRequest = {
            name: 'New Building',
            code: 'NEW',
            description: 'A new building created from the example component',
            address: '123 Example Street',
            is_active: true
        };

        createBuildingMutation.mutate(newBuilding);
    };

    const handleUpdateBuilding = (building: ApiBuilding) => {
        const updateData = {
            name: `${building.name} (Updated)`,
            description: `${building.description} - Updated at ${new Date().toLocaleString()}`
        };

        updateBuildingMutation.mutate({
            id: building.id,
            data: updateData
        });
    };

    const handleDeleteBuilding = (buildingId: number) => {
        if (window.confirm('Are you sure you want to delete this building?')) {
            deleteBuildingMutation.mutate(buildingId);
        }
    };

    // Determine which data to display
    const displayData = searchTerm.length > 2 ? searchResults : buildingsData;
    const displayLoading = searchTerm.length > 2 ? isSearching : isLoading;

    if (displayLoading) {
        return (
            <div className="p-4">
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="space-y-3">
                        {[...Array(3)].map((_, i) => (
                            <div key={i} className="h-20 bg-gray-200 rounded"></div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-4">
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <h3 className="text-red-800 font-medium">Error loading buildings</h3>
                    <p className="text-red-600 text-sm mt-1">{error.message}</p>
                    <button 
                        onClick={() => refetch()}
                        className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    const buildings = displayData?.data.buildings || [];

    return (
        <div className="p-4 max-w-4xl mx-auto">
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-4">Buildings Management</h1>
                
                {/* Search Input */}
                <div className="mb-4">
                    <input
                        type="text"
                        placeholder="Search buildings..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mb-4">
                    <button
                        onClick={handleCreateBuilding}
                        disabled={createBuildingMutation.isPending}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                    >
                        {createBuildingMutation.isPending ? 'Creating...' : 'Create Building'}
                    </button>
                    <button
                        onClick={() => refetch()}
                        className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                    >
                        Refresh
                    </button>
                </div>

                {/* Results Summary */}
                <div className="text-sm text-gray-600 mb-4">
                    Showing {displayData?.data.returned_count || 0} of {displayData?.data.total_count || 0} buildings
                    {searchTerm && ` for "${searchTerm}"`}
                </div>
            </div>

            {/* Buildings List */}
            <div className="space-y-4">
                {buildings.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        {searchTerm ? 'No buildings found matching your search.' : 'No buildings available.'}
                    </div>
                ) : (
                    buildings.map((building) => (
                        <div key={building.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                            <div className="flex justify-between items-start">
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <h3 className="text-lg font-semibold text-gray-900">{building.name}</h3>
                                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                            {building.code}
                                        </span>
                                        {!building.is_active && (
                                            <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                                                Inactive
                                            </span>
                                        )}
                                        {building.has_alerts && (
                                            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                                Has Alerts
                                            </span>
                                        )}
                                    </div>
                                    <p className="text-gray-600 text-sm mb-2">{building.description}</p>
                                    <p className="text-gray-500 text-xs mb-3">{building.address}</p>
                                    
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span className="text-gray-500">Zones:</span>
                                            <span className="ml-1 font-medium">{building.zone_count}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Floors:</span>
                                            <span className="ml-1 font-medium">{building.floor_count}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Rooms:</span>
                                            <span className="ml-1 font-medium">{building.room_count}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Devices:</span>
                                            <span className="ml-1 font-medium">{building.device_count}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="flex gap-2 ml-4">
                                    <button
                                        onClick={() => handleUpdateBuilding(building)}
                                        disabled={updateBuildingMutation.isPending}
                                        className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50"
                                    >
                                        {updateBuildingMutation.isPending ? 'Updating...' : 'Update'}
                                    </button>
                                    <button
                                        onClick={() => handleDeleteBuilding(building.id)}
                                        disabled={deleteBuildingMutation.isPending}
                                        className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50"
                                    >
                                        {deleteBuildingMutation.isPending ? 'Deleting...' : 'Delete'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))
                )}
            </div>

            {/* Pagination Info */}
            {displayData?.data.pagination.has_more && (
                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-500">
                        More buildings available. Implement pagination to load more.
                    </p>
                </div>
            )}
        </div>
    );
}
