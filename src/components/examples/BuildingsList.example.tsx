/**
 * Simple Buildings List Example
 * مثال بسيط لاستخدام building service
 */

import React from 'react';
import { useBuildings, type Building } from '@/infrastructure/api/buildings';

export function BuildingsList() {
    // استخدام الhook البسيط
    const { data, isLoading, error } = useBuildings();

    // لو بيحمل
    if (isLoading) {
        return (
            <div className="p-4">
                <div className="text-center">جاري تحميل المباني...</div>
            </div>
        );
    }

    // لو في خطأ
    if (error) {
        return (
            <div className="p-4">
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <h3 className="text-red-800 font-medium">خطأ في تحميل المباني</h3>
                    <p className="text-red-600 text-sm mt-1">{error.message}</p>
                </div>
            </div>
        );
    }

    const buildings = data?.data.buildings || [];

    return (
        <div className="p-4 max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">قائمة المباني</h1>

            <div className="text-sm text-gray-600 mb-4">عدد المباني: {data?.data.total_count || 0}</div>

            {/* Buildings List */}
            <div className="space-y-4">
                {buildings.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        {searchTerm ? 'No buildings found matching your search.' : 'No buildings available.'}
                    </div>
                ) : (
                    buildings.map((building) => (
                        <div key={building.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                            <div className="flex justify-between items-start">
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <h3 className="text-lg font-semibold text-gray-900">{building.name}</h3>
                                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                            {building.code}
                                        </span>
                                        {!building.is_active && (
                                            <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                                                Inactive
                                            </span>
                                        )}
                                        {building.has_alerts && (
                                            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                                Has Alerts
                                            </span>
                                        )}
                                    </div>
                                    <p className="text-gray-600 text-sm mb-2">{building.description}</p>
                                    <p className="text-gray-500 text-xs mb-3">{building.address}</p>

                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span className="text-gray-500">Zones:</span>
                                            <span className="ml-1 font-medium">{building.zone_count}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Floors:</span>
                                            <span className="ml-1 font-medium">{building.floor_count}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Rooms:</span>
                                            <span className="ml-1 font-medium">{building.room_count}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Devices:</span>
                                            <span className="ml-1 font-medium">{building.device_count}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
}
